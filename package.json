{"name": "Wheel of Names", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@reduxjs/toolkit": "^2.2.1", "canvas-confetti": "^1.9.3", "chroma-js": "^2.4.2", "clsx": "^2.1.1", "konva": "^9.3.16", "randomcolor": "^0.6.2", "react": "^18.2.0", "react-confetti": "^6.1.0", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "react-konva": "^18.2.10", "react-redux": "^9.1.0", "styled-components": "^6.1.14", "tailwind-merge": "^2.5.5", "zustand": "^4.5.1"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.16.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard-with-typescript": "^43.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-n": "^16.5.0", "eslint-plugin-prettier": "^5.1.2", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.0", "typescript": "^5.3.3", "vite": "^5.0.8"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}