name: Deploy to Firebase Hosting on merge
on:
  push:
    branches:
      - main
jobs:
  build_and_deploy:
    env:
      SVELTE_WHEEL_API_KEY: ${{ secrets.SVELTE_WHEEL_API_KEY }}
      PUBLIC_FIREBASE_API_KEY: ${{ vars.PUBLIC_FIREBASE_API_KEY }}
      PUBLIC_FIREBASE_AUTH_DOMAIN: ${{ vars.PUBLIC_FIREBASE_AUTH_DOMAIN }}
      PUBLIC_FIREBASE_PROJECT_ID: ${{ vars.PUBLIC_FIREBASE_PROJECT_ID }}
      PUBLIC_FIREBASE_STORAGE_BUCKET: ${{ vars.PUBLIC_FIREBASE_STORAGE_BUCKET }}
      PUBLIC_FIREBASE_MESSAGING_SENDER_ID: ${{ vars.PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}
      PUBLIC_FIREBASE_APP_ID: ${{ vars.PUBLIC_FIREBASE_APP_ID }}
      PUBLIC_FIREBASE_MEASUREMENT_ID: ${{ vars.PUBLIC_FIREBASE_MEASUREMENT_ID }}
      GCP_PROJECT_ID: ${{ vars.GCP_PROJECT_ID }}
      GCP_SERVICE_ID: ${{ vars.GCP_SERVICE_ID }}
      GCP_SERVICE_REGION: ${{ vars.GCP_SERVICE_REGION }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Install Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
      - name: Install pnpm and dependencies
        uses: pnpm/action-setup@v4
        with:
          version: 10
          run_install: true
      - name: Run unit tests
        run: pnpm test:unit
      - name: Set up Playwright
        run: npx playwright install --with-deps
      - name: Run integration tests
        run: pnpm test:integration
      - name: Build app
        run: npm run build
      - name: Copy necessary files to build directory
        run: cp package.json pnpm-lock.yaml Dockerfile build
      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.FIREBASE_DEPLOYER_SERVICE_ACCOUNT }}
      - name: Install Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
      - name: Clean up old versions
        run: (gcloud container images list-tags "$GCP_SERVICE_REGION-docker.pkg.dev/$GCP_PROJECT_ID/cloud-run-source-deploy/$GCP_SERVICE_ID" --filter='-tags:*' --format='get(digest)' --limit=unlimited | awk '{print "'"$GCP_SERVICE_REGION"'-docker.pkg.dev/'"$GCP_PROJECT_ID"'/cloud-run-source-deploy/'"$GCP_SERVICE_ID"'@" $1}' | xargs gcloud container images delete --quiet) || true
      - name: Deploy to Cloud Run
        run: gcloud run deploy "$GCP_SERVICE_ID" --source build --platform managed --memory 128Mi --region "$GCP_SERVICE_REGION" --allow-unauthenticated --project "$GCP_PROJECT_ID"
