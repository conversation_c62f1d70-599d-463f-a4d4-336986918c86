{"name": "svelte-wheel", "version": "0.0.1", "description": "A Wheel of Names clone built with SvelteKit", "author": "<PERSON><PERSON> <<EMAIL>> (https://gomander.dev)", "license": "GPL-3.0-only", "private": true, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "bp": "npm run build && npm run preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "npm run test:integration && npm run test:unit", "test:integration": "playwright test", "test:unit": "vitest", "generate-pwa-assets": "pwa-assets-generator --preset minimal-2023 static/images/icons/favicon.svg"}, "devDependencies": {"@playwright/test": "^1.51.0", "@skeletonlabs/skeleton": "^2.11.0", "@skeletonlabs/tw-plugin": "~0.4.1", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.19.2", "@sveltejs/vite-plugin-svelte": "^5.0.3", "@tailwindcss/forms": "~0.5.10", "@tailwindcss/typography": "~0.5.16", "@types/canvas-confetti": "^1.9.0", "@types/node": "^22.13.10", "@vite-pwa/assets-generator": "~0.2.6", "@vite-pwa/sveltekit": "^0.6.7", "autoprefixer": "^10.4.21", "editorconfig": "^2.0.1", "postcss": "^8.5.3", "postcss-load-config": "^6.0.1", "svelte": "^5.23.0", "svelte-check": "^4.1.5", "svelte-dnd-action": "~0.9.60", "tailwindcss": "^3.4.17", "tslib": "^2.8.1", "typescript": "^5.8.2", "vite": "^6.2.2", "vite-plugin-pwa": "~0.21.1", "vite-plugin-tailwind-purgecss": "~0.3.5", "vitest": "^3.0.8", "workbox-window": "^7.3.0"}, "dependencies": {"@floating-ui/dom": "^1.6.13", "@napi-rs/canvas": "~0.1.68", "browser-fs-access": "~0.35.0", "canvas-confetti": "^1.9.3", "firebase": "^11.4.0", "firebase-admin": "^13.2.0", "validate-color": "^2.2.4", "zod": "^3.24.2"}, "engines": {"node": ">=20", "pnpm": ">=8"}}