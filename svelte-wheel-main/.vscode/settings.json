{"tailwindCSS.classAttributes": ["class", "accent", "active", "aspectRatio", "background", "badge", "bgBackdrop", "bgDark", "bg<PERSON><PERSON><PERSON>", "bgLight", "blur", "border", "button", "buttonAction", "buttonBack", "buttonClasses", "buttonComplete", "button<PERSON><PERSON><PERSON>", "buttonNeutral", "buttonNext", "buttonPositive", "buttonTextCancel", "buttonTextConfirm", "buttonTextFirst", "buttonTextLast", "buttonTextNext", "buttonTextPrevious", "buttonTextSubmit", "caretClosed", "caretOpen", "chips", "color", "controlSeparator", "controlVariant", "cursor", "display", "element", "fill", "fillDark", "fillLight", "flex", "gap", "gridColumns", "height", "hover", "inactive", "indent", "justify", "meter", "padding", "position", "regionAnchor", "regionBackdrop", "regionBody", "regionCaption", "regionCaret", "regionCell", "regionChildren", "regionChipList", "regionChipWrapper", "regionCone", "regionContent", "regionControl", "regionDefault", "regionDrawer", "regionFoot", "regionFootCell", "regionFooter", "regionHead", "regionHeadCell", "regionHeader", "regionIcon", "regionInput", "regionInterface", "regionInterfaceText", "regionLabel", "regionLead", "regionLegend", "regionList", "regionListItem", "regionNavigation", "regionPage", "regionPanel", "regionRowHeadline", "regionRowMain", "regionSummary", "regionSymbol", "regionTab", "regionTrail", "ring", "rounded", "select", "shadow", "slotDefault", "slotFooter", "slotHeader", "slotLead", "slotMessage", "slotMeta", "slotPageContent", "slot<PERSON><PERSON><PERSON>ooter", "slotPageHeader", "slotSidebarLeft", "slotSidebarRight", "slotTrail", "spacing", "text", "track", "transition", "width", "zIndex"]}