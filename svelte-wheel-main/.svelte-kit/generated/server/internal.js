
import root from '../root.js';
import { set_building, set_prerendering } from '__sveltekit/environment';
import { set_assets } from '__sveltekit/paths';
import { set_manifest, set_read_implementation } from '__sveltekit/server';
import { set_private_env, set_public_env, set_safe_public_env } from '../../../node_modules/.pnpm/@sveltejs+kit@2.19.2_@sveltejs+vite-plugin-svelte@5.0.3_svelte@5.23.0_vite@6.2.2_@types_039f64c12fea705573bbcb3b60647c72/node_modules/@sveltejs/kit/src/runtime/shared-server.js';

export const options = {
	app_template_contains_nonce: false,
	csp: {"mode":"auto","directives":{"upgrade-insecure-requests":false,"block-all-mixed-content":false},"reportOnly":{"upgrade-insecure-requests":false,"block-all-mixed-content":false}},
	csrf_check_origin: true,
	embedded: false,
	env_public_prefix: 'PUBLIC_',
	env_private_prefix: '',
	hash_routing: false,
	hooks: null, // added lazily, via `get_hooks`
	preload_strategy: "modulepreload",
	root,
	service_worker: false,
	templates: {
		app: ({ head, body, assets, nonce, env }) => "<!doctype html>\n<html lang=\"en\" class=\"dark\">\n  <head>\n    <meta charset=\"utf-8\">\n    <link rel=\"icon\" href=\"" + assets + "/favicon.ico\" sizes=\"48x48\">\n    <link\n      rel=\"icon\"\n      href=\"" + assets + "/images/icons/favicon.svg\"\n      sizes=\"any\"\n      type=\"image/svg+xml\"\n    >\n    <link\n      rel=\"apple-touch-icon\"\n      href=\"" + assets + "/images/icons/apple-touch-icon-180x180.png\"\n      sizes=\"180x180\"\n    >\n    <meta\n      name=\"viewport\"\n      content=\"initial-scale=1, minimum-scale=1, maximum-scale=5, width=device-width\"\n    >\n    <link\n      rel=\"preload\"\n      href=\"" + assets + "/Quicksand.ttf\"\n      as=\"font\"\n      type=\"font/ttf\"\n      crossorigin\n    >\n    <link\n      rel=\"preload\"\n      as=\"style\"\n      onload=\"this.onload=null;this.rel='stylesheet'\"\n      href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/fontawesome.min.css\"\n      integrity=\"sha512-d0olNN35C6VLiulAobxYHZiXJmq+vl+BGIgAxQtD5+kqudro/xNMvv2yIHAciGHpExsIbKX3iLg+0B6d0k4+ZA==\"\n      crossorigin=\"anonymous\"\n      referrerpolicy=\"no-referrer\"\n    >\n    <link\n      rel=\"preload\"\n      as=\"style\"\n      onload=\"this.onload=null;this.rel='stylesheet'\"\n      href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/solid.min.css\"\n      integrity=\"sha512-pZlKGs7nEqF4zoG0egeK167l6yovsuL8ap30d07kA5AJUq+WysFlQ02DLXAmN3n0+H3JVz5ni8SJZnrOaYXWBA==\"\n      crossorigin=\"anonymous\"\n      referrerpolicy=\"no-referrer\"\n    >\n    <meta property=\"og:site_name\" content=\"Svelte Wheel\">\n    " + head + "\n    <title>Svelte Wheel</title>\n    <meta name=\"title\" content=\"Svelte Wheel\">\n    <meta name=\"description\" content=\"Enter texts onto the wheel and spin for a random result!\">\n    <meta property=\"og:url\" content=\"https://sveltewheel.com/\">\n    <meta property=\"og:type\" content=\"website\">\n    <meta property=\"og:title\" content=\"Svelte Wheel\">\n    <meta\n      property=\"og:description\"\n      content=\"Enter texts onto the wheel and spin for a random result!\"\n    >\n  </head>\n  <body data-sveltekit-preload-data=\"hover\" data-theme=\"skeleton\">\n    <div class=\"contents\">" + body + "</div>\n  </body>\n</html>\n",
		error: ({ status, message }) => "<!doctype html>\n<html lang=\"en\">\n\t<head>\n\t\t<meta charset=\"utf-8\" />\n\t\t<title>" + message + "</title>\n\n\t\t<style>\n\t\t\tbody {\n\t\t\t\t--bg: white;\n\t\t\t\t--fg: #222;\n\t\t\t\t--divider: #ccc;\n\t\t\t\tbackground: var(--bg);\n\t\t\t\tcolor: var(--fg);\n\t\t\t\tfont-family:\n\t\t\t\t\tsystem-ui,\n\t\t\t\t\t-apple-system,\n\t\t\t\t\tBlinkMacSystemFont,\n\t\t\t\t\t'Segoe UI',\n\t\t\t\t\tRoboto,\n\t\t\t\t\tOxygen,\n\t\t\t\t\tUbuntu,\n\t\t\t\t\tCantarell,\n\t\t\t\t\t'Open Sans',\n\t\t\t\t\t'Helvetica Neue',\n\t\t\t\t\tsans-serif;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\theight: 100vh;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\t.error {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tmax-width: 32rem;\n\t\t\t\tmargin: 0 1rem;\n\t\t\t}\n\n\t\t\t.status {\n\t\t\t\tfont-weight: 200;\n\t\t\t\tfont-size: 3rem;\n\t\t\t\tline-height: 1;\n\t\t\t\tposition: relative;\n\t\t\t\ttop: -0.05rem;\n\t\t\t}\n\n\t\t\t.message {\n\t\t\t\tborder-left: 1px solid var(--divider);\n\t\t\t\tpadding: 0 0 0 1rem;\n\t\t\t\tmargin: 0 0 0 1rem;\n\t\t\t\tmin-height: 2.5rem;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t.message h1 {\n\t\t\t\tfont-weight: 400;\n\t\t\t\tfont-size: 1em;\n\t\t\t\tmargin: 0;\n\t\t\t}\n\n\t\t\t@media (prefers-color-scheme: dark) {\n\t\t\t\tbody {\n\t\t\t\t\t--bg: #222;\n\t\t\t\t\t--fg: #ddd;\n\t\t\t\t\t--divider: #666;\n\t\t\t\t}\n\t\t\t}\n\t\t</style>\n\t</head>\n\t<body>\n\t\t<div class=\"error\">\n\t\t\t<span class=\"status\">" + status + "</span>\n\t\t\t<div class=\"message\">\n\t\t\t\t<h1>" + message + "</h1>\n\t\t\t</div>\n\t\t</div>\n\t</body>\n</html>\n"
	},
	version_hash: "gw1o0r"
};

export async function get_hooks() {
	let handle;
	let handleFetch;
	let handleError;
	let init;
	

	let reroute;
	let transport;
	

	return {
		handle,
		handleFetch,
		handleError,
		init,
		reroute,
		transport
	};
}

export { set_assets, set_building, set_manifest, set_prerendering, set_private_env, set_public_env, set_read_implementation, set_safe_public_env };
